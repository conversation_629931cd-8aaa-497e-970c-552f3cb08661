import React from 'react';
import Container from '../Layout/Container';
import useResponsive from '../../hooks/useResponsive';

const MasterclassSection = () => {
  const { isMobile } = useResponsive();

  return (
    <section id="masterclass" className="min-h-screen bg-gray-800 py-16 lg:py-24">
      <Container>
        {/* Section Header */}
        <div className="text-center mb-12 lg:mb-16">
          <h2 className={`${isMobile ? 'text-3xl' : 'text-4xl lg:text-5xl'} font-bold mb-4 text-white`}>
            Masterclass Sessions
          </h2>
          <p className={`${isMobile ? 'text-base' : 'text-lg'} text-gray-300 mb-6 max-w-3xl mx-auto`}>
            Deep-dive learning sessions with industry experts. Limited seats available for intensive, hands-on workshops.
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-orange-500 to-amber-500 mx-auto rounded-full"></div>
        </div>

        {/* Masterclass Grid */}
        <div className={`grid ${isMobile ? 'grid-cols-1' : 'grid-cols-1 lg:grid-cols-2'} gap-6 lg:gap-8`}>
          {masterclasses.map((masterclass) => (
            <Card
              key={masterclass.id}
              variant="glass"
              className={`${!masterclass.available ? 'opacity-75' : ''} hover:scale-105 transition-all duration-300`}
            >
              {/* Header */}
              <div className="mb-6">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="text-xl font-bold text-white flex-grow pr-4">
                    {masterclass.title}
                  </h3>
                  <div className={`px-3 py-1 rounded-full text-xs font-medium ${getLevelColor(masterclass.level)} flex-shrink-0`}>
                    {masterclass.level}
                  </div>
                </div>

                <div className="text-orange-300 font-medium mb-2">
                  {masterclass.instructor}
                </div>

                <div className="flex flex-wrap gap-4 text-sm text-gray-400">
                  <span>⏱️ {masterclass.duration}</span>
                  <span>👥 {masterclass.capacity}</span>
                </div>
              </div>

              {/* Description */}
              <p className="text-gray-300 text-sm mb-6 leading-relaxed">
                {masterclass.description}
              </p>

              {/* Highlights */}
              <div className="mb-6">
                <h4 className="text-white font-semibold mb-3">What You'll Learn:</h4>
                <ul className="space-y-2">
                  {masterclass.highlights.map((highlight, index) => (
                    <li key={index} className="flex items-start space-x-3">
                      <div className="w-4 h-4 rounded-full bg-orange-500/20 flex items-center justify-center mt-0.5 flex-shrink-0">
                        <div className="w-1.5 h-1.5 rounded-full bg-orange-400"></div>
                      </div>
                      <span className="text-gray-300 text-sm">{highlight}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Footer */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-700/50">
                <div className="text-orange-300 font-bold text-lg">
                  {masterclass.price}
                </div>
                <Button
                  variant={masterclass.available ? 'primary' : 'secondary'}
                  size="md"
                  disabled={!masterclass.available}
                >
                  {masterclass.available ? 'Reserve Seat' : 'Sold Out'}
                </Button>
              </div>
            </Card>
          ))}
        </div>

        {/* Additional Information */}
        <div className="mt-12 lg:mt-16">
          <Card variant="glass" className="max-w-4xl mx-auto">
            <div className="text-center mb-6">
              <h3 className="text-2xl font-bold text-white mb-4">Masterclass Information</h3>
            </div>

            <div className={`grid ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2'} gap-8`}>
              <div>
                <h4 className="text-white font-semibold mb-3">What's Included:</h4>
                <ul className="space-y-2 text-gray-300 text-sm">
                  <li>• 90-minute intensive session</li>
                  <li>• Hands-on exercises and activities</li>
                  <li>• Take-home resources and templates</li>
                  <li>• Certificate of completion</li>
                  <li>• Access to exclusive materials</li>
                </ul>
              </div>

              <div>
                <h4 className="text-white font-semibold mb-3">Important Notes:</h4>
                <ul className="space-y-2 text-gray-300 text-sm">
                  <li>• Limited seats per session</li>
                  <li>• Separate registration required</li>
                  <li>• Materials provided in English</li>
                  <li>• Laptop recommended for some sessions</li>
                  <li>• Refundable until 48 hours before</li>
                </ul>
              </div>
            </div>
          </Card>
        </div>
      </Container>
    </section>
  );
};

export default MasterclassSection;
